import chess
import chess.engine
import chess.polyglot
from typing import List, Optional, Tuple
import heapq
from dataclasses import dataclass, field
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration constants
STOCKFISH_PATH = "stockfish"
POLYGLOT_BOOK_PATH = "Performance.bin"
MAX_DEPTH = 6
BEAM_WIDTH = 10


@dataclass
class SearchNode:
    """Represents a node in the backward search tree."""
    position: chess.Board
    cost: float
    depth: int
    parent: Optional['SearchNode'] = None
    move_from_parent: Optional[chess.Move] = None

    def __lt__(self, other):
        """For priority queue ordering (lower cost = higher priority)."""
        return self.cost < other.cost


def load_engine(stockfish_path: str) -> chess.engine.SimpleEngine:
    """
    Launch and return a Stockfish engine instance for evaluations.

    Args:
        stockfish_path: Path to the Stockfish binary.

    Returns:
        An active chess.engine.SimpleEngine instance.

    Notes:
        The engine should be terminated with .quit() after use.
    """
    try:
        engine = chess.engine.SimpleEngine.popen_uci(stockfish_path)
        logger.info(f"Stockfish engine loaded from: {stockfish_path}")
        return engine
    except Exception as e:
        logger.error(f"Failed to load Stockfish engine: {e}")
        raise


def load_opening_book(book_path: str):
    """
    Open and return a Polyglot opening book reader.

    Args:
        book_path: Path to the Polyglot book (e.g., performance.bin).

    Returns:
        A chess.polyglot.Reader instance for fast lookup of moves and weights.
    """
    try:
        book = chess.polyglot.open_reader(book_path)
        logger.info(f"Opening book loaded from: {book_path}")
        return book
    except Exception as e:
        logger.error(f"Failed to load opening book: {e}")
        raise


def calculate_position_similarity(pos1: chess.Board, pos2: chess.Board) -> float:
    """
    Calculate similarity between two chess positions.

    Args:
        pos1: First chess position
        pos2: Second chess position

    Returns:
        Similarity score (0.0 = completely different, 1.0 = identical)
    """
    if pos1.fen() == pos2.fen():
        return 1.0

    # Count matching pieces
    matching_pieces = 0
    total_pieces = 0

    for square in chess.SQUARES:
        piece1 = pos1.piece_at(square)
        piece2 = pos2.piece_at(square)

        if piece1 is not None:
            total_pieces += 1
            if piece1 == piece2:
                matching_pieces += 1
        elif piece2 is not None:
            total_pieces += 1

    if total_pieces == 0:
        return 1.0

    return matching_pieces / total_pieces


def generate_successors(position: chess.Board) -> List[Tuple[chess.Board, chess.Move]]:
    """
    Generate all legal successor positions from the given position.

    Args:
        position: Current chess position

    Returns:
        List of (successor_position, move) tuples
    """
    successors = []

    for move in position.legal_moves:
        successor = position.copy()
        successor.push(move)
        successors.append((successor, move))

    return successors


def evaluate_transition(prev: chess.Board, next: chess.Board, engine: chess.engine.SimpleEngine) -> float:
    """
    Compute a "transition cost" between two consecutive positions.

    Args:
        prev: The predecessor board position.
        next: The successor board position.
        engine: An active Stockfish instance to compute centipawn evaluations.

    Returns:
        A float representing the cost, e.g., abs(eval(prev) - eval(next)).

    Notes:
        A lower cost indicates a more plausible human/engine move sequence.
    """
    try:
        # Get evaluations for both positions
        prev_eval = engine.analyse(prev, chess.engine.Limit(depth=10))['score'].relative.score(mate_score=10000)
        next_eval = engine.analyse(next, chess.engine.Limit(depth=10))['score'].relative.score(mate_score=10000)

        # Calculate the absolute difference in centipawns
        if prev_eval is None:
            prev_eval = 0
        if next_eval is None:
            next_eval = 0

        cost = abs(prev_eval - next_eval)
        return cost

    except Exception as e:
        logger.warning(f"Evaluation failed: {e}")
        return 1000.0  # High cost for failed evaluations


def find_book_line_to_root(position: chess.Board, book) -> Optional[List[chess.Move]]:
    """
    Attempt to connect a position directly to the starting position using
    only book moves (Polyglot entries).

    Args:
        position: A chess.Board representing the current position.
        book: A Polyglot Reader for querying the opening book.

    Returns:
        A list of chess.Move objects forming a valid book line from the
        start position to `position`, or None if no book path is found.

    Notes:
        This avoids unnecessary search once a known opening path is found.
    """
    # Start from the initial position
    board = chess.Board()
    moves = []

    try:
        while board.fen() != position.fen():
            # Look for book moves from current position
            book_moves = list(book.find_all(board))

            if not book_moves:
                return None  # No book continuation

            # Try each book move to see if any leads toward our target
            found_move = None
            for entry in book_moves:
                test_board = board.copy()
                test_board.push(entry.move)

                # Simple heuristic: if this move gets us closer to target position
                # (measured by number of different pieces), use it
                if test_board.fen() == position.fen():
                    found_move = entry.move
                    break

                # For now, just take the highest-weighted move
                if found_move is None:
                    found_move = entry.move

            if found_move is None:
                return None

            board.push(found_move)
            moves.append(found_move)

            # Prevent infinite loops
            if len(moves) > 50:
                return None

        return moves

    except Exception as e:
        logger.warning(f"Book line search failed: {e}")
        return None


def calculate_heuristic_cost(current_pos: chess.Board, target_pos: chess.Board, engine: chess.engine.SimpleEngine) -> float:
    """
    Calculate heuristic cost to guide forward search toward target position.

    Args:
        current_pos: Current position in search
        target_pos: Target position we want to reach
        engine: Engine for position evaluation

    Returns:
        Heuristic cost (lower = better, closer to target)
    """
    # Position similarity (piece placement)
    similarity = calculate_position_similarity(current_pos, target_pos)
    position_cost = (1.0 - similarity) * 1000  # Scale to centipawn range

    try:
        # Evaluation difference
        current_eval = engine.analyse(current_pos, chess.engine.Limit(depth=8))['score'].relative.score(mate_score=10000)
        target_eval = engine.analyse(target_pos, chess.engine.Limit(depth=8))['score'].relative.score(mate_score=10000)

        if current_eval is None:
            current_eval = 0
        if target_eval is None:
            target_eval = 0

        eval_cost = abs(current_eval - target_eval) * 0.1  # Weight evaluation difference less

        return position_cost + eval_cost

    except Exception as e:
        logger.warning(f"Heuristic evaluation failed: {e}")
        return position_cost + 100.0


def forward_search(target: chess.Board,
                  engine: chess.engine.SimpleEngine,
                  book,
                  max_depth: int,
                  beam_width: int) -> List[List[chess.Move]]:
    """
    Perform a guided forward search from the starting position toward the target
    position, using heuristics to guide the search.

    Args:
        target: The target chess position (Board).
        engine: Stockfish engine for evaluation.
        book: Polyglot book for opening moves.
        max_depth: Maximum plies to explore.
        beam_width: How many best candidates to keep at each step.

    Returns:
        A list of candidate move sequences ordered by plausibility.
    """
    logger.info(f"Starting forward search to target position with max_depth={max_depth}, beam_width={beam_width}")

    # Priority queue for beam search (min-heap based on heuristic cost)
    frontier = []

    # Initialize with starting position
    start_pos = chess.Board()
    start_cost = calculate_heuristic_cost(start_pos, target, engine)
    start_node = SearchNode(position=start_pos, cost=start_cost, depth=0)
    heapq.heappush(frontier, start_node)

    # Track completed paths
    completed_paths = []

    # Track visited positions to avoid cycles
    visited = set()
    visited.add(start_pos.fen())

    while frontier and len(completed_paths) < beam_width:
        # Get the most promising node
        current_node = heapq.heappop(frontier)

        logger.debug(f"Exploring node at depth {current_node.depth}, cost {current_node.cost:.2f}")

        # Check if we've reached the target position
        if current_node.position.fen() == target.fen():
            path = reconstruct_path_from_node(current_node)
            completed_paths.append(path)
            logger.info(f"Found complete path with cost {current_node.cost:.2f}")
            continue

        # Don't expand beyond max depth
        if current_node.depth >= max_depth:
            continue

        # Generate successor positions
        successors = generate_successors(current_node.position)

        # Prioritize book moves if available
        book_moves = []
        if current_node.depth < 20:  # Only check book for early moves
            try:
                book_entries = list(book.find_all(current_node.position))
                book_moves = [entry.move for entry in book_entries]
            except:
                pass

        # Evaluate each successor
        candidates = []
        for succ_pos, move in successors:
            succ_fen = succ_pos.fen()

            # Skip if already visited
            if succ_fen in visited:
                continue

            # Calculate heuristic cost to target
            heuristic_cost = calculate_heuristic_cost(succ_pos, target, engine)

            # Bonus for book moves
            if move in book_moves:
                heuristic_cost *= 0.5  # Prefer book moves

            candidates.append((succ_pos, move, heuristic_cost))

        # Sort by cost and take best candidates
        candidates.sort(key=lambda x: x[2])
        best_candidates = candidates[:beam_width]

        # Add best candidates to frontier
        for succ_pos, move, cost in best_candidates:
            succ_node = SearchNode(
                position=succ_pos,
                cost=cost,
                depth=current_node.depth + 1,
                parent=current_node,
                move_from_parent=move
            )

            heapq.heappush(frontier, succ_node)
            visited.add(succ_pos.fen())

        # Prune frontier to prevent explosion
        if len(frontier) > beam_width * 3:
            frontier = heapq.nsmallest(beam_width * 2, frontier)
            heapq.heapify(frontier)

    logger.info(f"Search completed. Found {len(completed_paths)} candidate paths.")
    return completed_paths


def reconstruct_path_from_node(node: SearchNode) -> List[chess.Move]:
    """
    Reconstruct the move sequence from start to the given node.

    Args:
        node: The final node in the search path.

    Returns:
        A list of chess.Move objects from start to target position.
    """
    moves = []
    current = node

    # Collect all moves from the path
    while current is not None and current.move_from_parent is not None:
        moves.append(current.move_from_parent)
        current = current.parent

    # Reverse to get start-to-target order
    moves.reverse()
    return moves





def reconstruct_line(history: List[chess.Board]) -> List[chess.Move]:
    """
    Convert a sequence of Board states into a list of Move objects
    representing the game line from start to target.

    Args:
        history: List of board states from start to target (inclusive).

    Returns:
        A list of chess.Move objects representing the moves played.
    """
    moves = []

    for i in range(len(history) - 1):
        current_board = history[i].copy()
        next_board = history[i + 1]

        # Find the move that transforms current to next
        for move in current_board.legal_moves:
            test_board = current_board.copy()
            test_board.push(move)
            if test_board.fen() == next_board.fen():
                moves.append(move)
                break

    return moves


def main() -> None:
    """
    Example driver function:
        - Load Stockfish and Polyglot book.
        - Define a target FEN.
        - Run forward_search() to find plausible move histories.
        - Print the top N candidate lines.
    """
    # Example target position: Complex middlegame position
    target_fen = "r1bqk2r/pppp1ppp/2n2n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 4 5"

    logger.info("Chess Position Reconstruction Demo")
    logger.info("=" * 50)
    logger.info("This demo implements forward search to find plausible game histories")
    logger.info("that lead to a given chess position using Stockfish evaluation")
    logger.info("and Polyglot opening book optimization.")
    logger.info("")
    logger.info(f"Target position: {target_fen}")

    try:
        # Load engine and book
        logger.info("Loading Stockfish engine...")
        engine = load_engine(STOCKFISH_PATH)

        logger.info("Loading opening book...")
        book = load_opening_book(POLYGLOT_BOOK_PATH)

        # Create target position
        target_board = chess.Board(target_fen)
        logger.info(f"Target position loaded:\n{target_board}")

        # Run forward search
        logger.info("Starting forward search...")
        candidate_lines = forward_search(
            target=target_board,
            engine=engine,
            book=book,
            max_depth=MAX_DEPTH,
            beam_width=BEAM_WIDTH
        )

        # Display results
        logger.info("\nSearch Results:")
        logger.info("=" * 50)

        if not candidate_lines:
            logger.info("No candidate lines found.")
        else:
            for i, line in enumerate(candidate_lines[:3], 1):  # Show top 3
                logger.info(f"\nCandidate {i}:")

                # Convert moves to SAN notation for display
                board = chess.Board()
                san_moves: list[str] = []

                for move in line:
                    san_moves.append(board.san(move))
                    board.push(move)

                # Format as numbered moves
                move_pairs: list[str] = []
                for j in range(0, len(san_moves), 2):
                    move_num = (j // 2) + 1
                    white_move = san_moves[j] if j < len(san_moves) else ""
                    black_move = san_moves[j + 1] if j + 1 < len(san_moves) else ""

                    if black_move:
                        move_pairs.append(f"{move_num}.{white_move} {black_move}")
                    else:
                        move_pairs.append(f"{move_num}.{white_move}")

                logger.info(" ".join(move_pairs))
                logger.info(f"Final position: {board.fen()}")

        # Cleanup
        engine.quit()
        book.close()

    except Exception as e:
        logger.error(f"Demo failed: {e}")
        raise


if __name__ == "__main__":
    main()